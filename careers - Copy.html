<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers | Solving Tomorrow</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
        }

        header {
            background-color: var(--dark);
            color: var(--light);
            padding: 1rem 2rem;
            position: fixed;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .logo span {
            color: var(--light);
        }

        .back-link {
            color: var(--light);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--primary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 8rem 2rem 4rem;
        }

        .page-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title h1 {
            font-size: 2.5rem;
            color: var(--secondary);
            margin-bottom: 1rem;
        }

        .page-title h1 span {
            color: var(--primary);
        }

        .page-title p {
            color: var(--grey);
            font-size: 1.1rem;
        }

        .culture-section {
            background-color: white;
            padding: 3rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 3rem;
        }

        .culture-section h2 {
            font-size: 1.8rem;
            color: var(--secondary);
            margin-bottom: 2rem;
            text-align: center;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .benefit-card {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid var(--primary);
        }

        .benefit-card i {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .benefit-card h3 {
            font-size: 1.2rem;
            color: var(--secondary);
            margin-bottom: 1rem;
        }

        .benefit-card p {
            color: var(--grey);
        }

        .jobs-section {
            margin-top: 3rem;
        }

        .jobs-section h2 {
            font-size: 1.8rem;
            color: var(--secondary);
            margin-bottom: 2rem;
            text-align: center;
        }

        .job-card {
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
            border-left: 4px solid var(--primary);
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .job-title {
            font-size: 1.3rem;
            color: var(--secondary);
            margin-bottom: 0.5rem;
        }

        .job-type {
            background-color: var(--primary);
            color: white;
            padding: 0.3rem 1rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .job-details {
            display: flex;
            gap: 2rem;
            margin-bottom: 1rem;
            color: var(--grey);
        }

        .job-details span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .job-description {
            color: var(--grey);
            margin-bottom: 1rem;
        }

        .job-requirements {
            margin-bottom: 1.5rem;
        }

        .job-requirements h4 {
            color: var(--secondary);
            margin-bottom: 0.5rem;
        }

        .job-requirements ul {
            margin-left: 1.5rem;
            color: var(--grey);
        }

        .apply-btn {
            background-color: var(--primary);
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .apply-btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }

        .no-openings {
            text-align: center;
            padding: 3rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .no-openings i {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .no-openings h3 {
            color: var(--secondary);
            margin-bottom: 1rem;
        }

        .no-openings p {
            color: var(--grey);
            margin-bottom: 2rem;
        }

        .contact-section {
            background-color: var(--dark);
            color: var(--light);
            padding: 3rem;
            border-radius: 10px;
            text-align: center;
            margin-top: 3rem;
        }

        .contact-section h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .contact-section p {
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn {
            display: inline-block;
            background-color: var(--primary);
            color: var(--light);
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid var(--primary);
            margin: 0.5rem;
        }

        .btn:hover {
            background-color: transparent;
            color: var(--primary);
            transform: translateY(-3px);
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <a href="solving.html" class="logo">Solving<span>Tomorrow</span></a>
            <a href="solving.html" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Main Site
            </a>
        </nav>
    </header>

    <div class="container">
        <div class="page-title">
            <h1>Join Our <span>Team</span></h1>
            <p>Help us revolutionize education in South Africa</p>
        </div>

        <div class="culture-section">
            <h2>Why Work at Solving Tomorrow?</h2>
            <p style="text-align: center; color: var(--grey); margin-bottom: 2rem;">We're building the future of education in South Africa. Join a passionate team of innovators, educators, and technologists making a real difference in students' lives.</p>
            
            <div class="benefits-grid">
                <div class="benefit-card">
                    <i class="fas fa-rocket"></i>
                    <h3>Innovation & Impact</h3>
                    <p>Work on cutting-edge AI technology that directly improves educational outcomes for thousands of students.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-users"></i>
                    <h3>Collaborative Culture</h3>
                    <p>Join a diverse, inclusive team where every voice matters and collaboration drives our success.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>Growth Opportunities</h3>
                    <p>Rapid company growth means accelerated career development and leadership opportunities.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-balance-scale"></i>
                    <h3>Work-Life Balance</h3>
                    <p>Flexible working arrangements, remote options, and a focus on sustainable productivity.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-graduation-cap"></i>
                    <h3>Learning & Development</h3>
                    <p>Continuous learning opportunities, conference attendance, and professional development budget.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-heart"></i>
                    <h3>Meaningful Mission</h3>
                    <p>Every day, you'll know your work is making education more accessible and effective for South African students.</p>
                </div>
            </div>
        </div>

        <div class="jobs-section">
            <h2>Current Openings</h2>
            
            <!-- Future job openings will be added here -->
            <div class="no-openings">
                <i class="fas fa-briefcase"></i>
                <h3>No Current Openings</h3>
                <p>We're currently focused on our core team, but we're always looking for exceptional talent. Check back soon or reach out to us directly if you're passionate about educational technology.</p>
                <a href="mailto:<EMAIL>" class="apply-btn">Send Us Your CV</a>
            </div>

            <!-- Example job posting structure for future use -->
            <!--
            <div class="job-card">
                <div class="job-header">
                    <div>
                        <h3 class="job-title">Senior AI Engineer</h3>
                        <div class="job-details">
                            <span><i class="fas fa-map-marker-alt"></i> Durban / Remote</span>
                            <span><i class="fas fa-clock"></i> Full-time</span>
                            <span><i class="fas fa-money-bill-wave"></i> R600,000 - R800,000</span>
                        </div>
                    </div>
                    <span class="job-type">Full-time</span>
                </div>
                
                <p class="job-description">
                    We're looking for a Senior AI Engineer to lead the development of our machine learning algorithms and natural language processing capabilities for the South African education market.
                </p>
                
                <div class="job-requirements">
                    <h4>Requirements:</h4>
                    <ul>
                        <li>5+ years experience in machine learning and AI development</li>
                        <li>Strong background in NLP and educational technology</li>
                        <li>Experience with Python, TensorFlow, and cloud platforms</li>
                        <li>Understanding of South African education system preferred</li>
                    </ul>
                </div>
                
                <a href="mailto:<EMAIL>?subject=Application: Senior AI Engineer" class="apply-btn">Apply Now</a>
            </div>
            -->
        </div>

        <div class="contact-section">
            <h3>Don't See the Right Role?</h3>
            <p>We're always interested in connecting with talented individuals who share our passion for education and technology. Send us your CV and tell us how you'd like to contribute to our mission.</p>
            <a href="mailto:<EMAIL>" class="btn">Get in Touch</a>
        </div>
    </div>
</body>
</html>
