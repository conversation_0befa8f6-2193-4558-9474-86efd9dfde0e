<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solving Tomorrow | AI-Powered EdTech Solutions</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #FF6B35;
            --primary-dark: #E05A2B;
            --secondary: #2D2D2D;
            --light: #F7F7F7;
            --dark: #1A1A1A;
            --grey: #6C757D;
            --light-grey: #E9ECEF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Navigation */
        header {
            background-color: var(--dark);
            color: var(--light);
            padding: 1rem 2rem;
            position: fixed;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        header.scrolled {
            padding: 0.5rem 2rem;
            background-color: rgba(26, 26, 26, 0.95);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .logo span {
            color: var(--light);
        }

        .nav-links {
            display: flex;
            list-style: none;
        }

        .nav-links li {
            margin-left: 2rem;
        }

        .nav-links a {
            color: var(--light);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            background: var(--primary);
            bottom: -5px;
            left: 0;
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--light);
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(45, 45, 45, 0.8), rgba(45, 45, 45, 0.8)), url('https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80') no-repeat center center/cover;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: var(--light);
            padding: 0 2rem;
        }

        .hero-content {
            max-width: 800px;
            animation: fadeIn 1.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero h1 span {
            color: var(--primary);
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn {
            display: inline-block;
            background-color: var(--primary);
            color: var(--light);
            padding: 0.8rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid var(--primary);
        }

        .btn:hover {
            background-color: transparent;
            color: var(--primary);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(255, 107, 53, 0.2);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            margin-left: 1rem;
        }

        .btn-outline:hover {
            background-color: var(--primary);
            color: var(--light);
        }

        /* About Section */
        .section {
            padding: 6rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--secondary);
            margin-bottom: 1rem;
        }

        .section-title h2 span {
            color: var(--primary);
        }

        .section-title::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 4px;
            background-color: var(--primary);
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .about-content {
            display: flex;
            align-items: center;
            gap: 3rem;
        }

        .about-text {
            flex: 1;
        }

        .about-text h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: var(--secondary);
        }

        .about-text p {
            margin-bottom: 1.5rem;
            color: var(--grey);
        }

        .about-image {
            flex: 1;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.5s ease;
        }

        .about-image:hover {
            transform: scale(1.03);
        }

        .about-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        /* Team Section */
        .team {
            background-color: var(--light-grey);
        }

        .team-members {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .team-member {
            background-color: var(--light);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            text-align: center;
        }

        .team-member:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .member-image {
            width: 100%;
            height: 300px;
            overflow: hidden;
        }

        .member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .team-member:hover .member-image img {
            transform: scale(1.1);
        }

        .member-info {
            padding: 1.5rem;
        }

        .member-info h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--secondary);
        }

        .member-info .role {
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 1rem;
            display: block;
        }

        .member-info p {
            color: var(--grey);
            margin-bottom: 1.5rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: var(--light-grey);
            color: var(--secondary);
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background-color: var(--primary);
            color: var(--light);
        }

        /* Revenue Model Section */
        .revenue-model {
            background-color: var(--dark);
            color: var(--light);
        }

        .revenue-model .section-title h2 {
            color: var(--light);
        }

        .revenue-content {
            display: flex;
            flex-direction: column;
            gap: 3rem;
        }

        .revenue-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .revenue-intro p {
            opacity: 0.9;
        }

        .revenue-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .revenue-card {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 2rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .revenue-card:hover {
            transform: translateY(-5px);
            background-color: rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .revenue-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .revenue-card p {
            margin-bottom: 1.5rem;
            opacity: 0.8;
        }

        .chart-container {
            margin-top: 3rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chart-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .chart-controls button {
            background-color: var(--primary);
            color: var(--light);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .chart-controls button:hover {
            background-color: var(--primary-dark);
        }

        .chart-controls button.active {
            background-color: var(--light);
            color: var(--primary);
        }

        .revenue-calculator {
            margin-top: 3rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .calculator-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 0.8rem;
            border-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--light);
            font-family: 'Poppins', sans-serif;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .calculator-results {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: rgba(255, 107, 53, 0.1);
            border-radius: 5px;
            border-left: 4px solid var(--primary);
        }

        .calculator-results h4 {
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
        }

        .result-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .result-item .label {
            font-weight: 500;
        }

        .result-item .value {
            font-weight: 600;
            color: var(--primary);
        }

        /* Footer */
        footer {
            background-color: var(--secondary);
            color: var(--light);
            padding: 4rem 2rem 2rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 3rem;
        }

        .footer-column h3 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary);
        }

        .footer-column p, .footer-column a {
            opacity: 0.8;
            margin-bottom: 1rem;
            display: block;
            color: var(--light);
            text-decoration: none;
            transition: opacity 0.3s ease;
        }

        .footer-column a:hover {
            opacity: 1;
            color: var(--primary);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            margin-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0.7;
        }

        /* Animations */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .about-content {
                flex-direction: column;
            }
            
            .about-image {
                order: -1;
                max-width: 600px;
                margin: 0 auto;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            .nav-links {
                position: fixed;
                top: 80px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 80px);
                background-color: var(--dark);
                flex-direction: column;
                align-items: center;
                justify-content: center;
                transition: all 0.5s ease;
                padding: 2rem;
            }

            .nav-links.active {
                left: 0;
            }

            .nav-links li {
                margin: 1rem 0;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .btn {
                padding: 0.6rem 1.5rem;
            }

            .section-title h2 {
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .hero h1 {
                font-size: 2rem;
            }

            .btn-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .btn-outline {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <header id="header">
        <nav>
            <a href="#" class="logo">Solving<span>Tomorrow</span></a>
            <button class="mobile-menu-btn" id="mobileMenuBtn">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="navLinks">
                <li><a href="#about">About</a></li>
                <li><a href="#team">Team</a></li>
                <li><a href="#revenue">Revenue Model</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>Empowering Education Through <span>AI-Driven Learning</span></h1>
            <p>Lumerous: South Africa's first CAPS-aligned AI learning platform bridging gaps in education with 24/7 personalized tutoring.</p>
            <div class="btn-container">
                <a href="#about" class="btn">Explore Our Solution</a>
                <a href="#revenue" class="btn btn-outline">Revenue Projections</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section" id="about">
        <div class="section-title">
            <h2>About <span>Solving Tomorrow</span></h2>
            <p>Transforming education through innovative technology</p>
        </div>
        <div class="about-content">
            <div class="about-text">
                <h3>Johannesburg-Based EdTech Innovator</h3>
                <p>Founded in 2025, Solving Tomorrow is a BEE-compliant, POPIA-certified startup dedicated to revolutionizing South Africa's education system. Our flagship product, Lumerous, is an AI-powered e-learning platform designed specifically for the South African curriculum.</p>
                <p>With initial capital of R800,000 from seed funding and reinvested service revenue, we've developed a fully functional MVP set to launch in July 2025. Our platform is currently being piloted in 3 schools with plans to expand to 30 schools by the end of Year 1.</p>
                <p>Our mission is to make quality education accessible to all South African learners through personalized, AI-driven learning experiences that complement traditional classroom teaching.</p>
                <a href="#team" class="btn">Meet Our Team</a>
            </div>
            <div class="about-image floating">
                <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80" alt="Students learning with technology">
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="section team" id="team">
        <div class="section-title">
            <h2>Our <span>Dream Team</span></h2>
            <p>The brilliant minds behind Solving Tomorrow</p>
        </div>
        <div class="team-members">
            <!-- Team Member 1 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Nomsa Khumalo">
                </div>
                <div class="member-info">
                    <h3>Nomsa Khumalo</h3>
                    <span class="role">CEO & Founder</span>
                    <p>Former Google Education lead with 10+ years in edtech. Holds a PhD in Computer Science from Wits University.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            
            <!-- Team Member 2 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Thabo Mbeki">
                </div>
                <div class="member-info">
                    <h3>Thabo Mbeki</h3>
                    <span class="role">CTO</span>
                    <p>AI specialist with 8 years at Amazon AI. Developed NLP models for 3 African languages. MSc in Machine Learning.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            
            <!-- Team Member 3 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Lerato Moloi">
                </div>
                <div class="member-info">
                    <h3>Lerato Moloi</h3>
                    <span class="role">Head of Education</span>
                    <p>Former DBE curriculum specialist. 15 years teaching experience. PhD in Education from UCT.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            
            <!-- Team Member 4 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Sipho Dlamini">
                </div>
                <div class="member-info">
                    <h3>Sipho Dlamini</h3>
                    <span class="role">Lead Developer</span>
                    <p>Full-stack developer specializing in edtech platforms. Built 3 successful SA education apps.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            
            <!-- Team Member 5 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Amahle Nkosi">
                </div>
                <div class="member-info">
                    <h3>Amahle Nkosi</h3>
                    <span class="role">UX/UI Designer</span>
                    <p>Former head of design at GetSmarter. Specializes in accessible learning interfaces.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-behance"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            
            <!-- Team Member 6 -->
            <div class="team-member">
                <div class="member-image">
                    <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="James Peterson">
                </div>
                <div class="member-info">
                    <h3>James Peterson</h3>
                    <span class="role">CFO</span>
                    <p>Former Deloitte financial consultant. Specializes in startup financing and edtech monetization.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Revenue Model Section -->
    <section class="section revenue-model" id="revenue">
        <div class="section-title">
            <h2>Revenue <span>Model</span></h2>
            <p>Sustainable growth through multiple revenue streams</p>
        </div>
        <div class="revenue-content">
            <div class="revenue-intro">
                <p>Our financial model is built on recurring SaaS subscriptions with tiered pricing. With R800,000 initial capital and R300,000 monthly fixed costs, we project breakeven at 1,500 active users (R300,000 monthly revenue). Explore our projections below.</p>
            </div>
            
            <div class="revenue-cards">
                <div class="revenue-card">
                    <h3>Individual Subscriptions</h3>
                    <p>R200/month per student or tutor. Our core revenue stream targeting private users who want supplemental learning support.</p>
                    <p><strong>Projection:</strong> 60% of total revenue by Year 2</p>
                </div>
                
                <div class="revenue-card">
                    <h3>School Licenses</h3>
                    <p>R5,000/month per school (50+ users). Bulk pricing for institutions with admin dashboards and analytics.</p>
                    <p><strong>Projection:</strong> 30 schools by Year 1 (R150,000/month)</p>
                </div>
                
                <div class="revenue-card">
                    <h3>Premium Features</h3>
                    <p>Add-ons like advanced analytics (R50/user/month) and certification (R100/test). High-margin upsell opportunities.</p>
                    <p><strong>Projection:</strong> 15% adoption rate by Year 2</p>
                </div>
            </div>
            
            <div class="chart-container">
                <h3>Revenue Projections</h3>
                <p>Select a growth scenario to visualize our financial trajectory</p>
                
                <div class="chart-controls">
                    <button class="active" data-scenario="optimistic">Optimistic (500 users/month)</button>
                    <button data-scenario="realistic">Realistic (200 users/month)</button>
                    <button data-scenario="conservative">Conservative (100 users/month)</button>
                </div>
                
                <canvas id="revenueChart"></canvas>
            </div>
            
            <div class="revenue-calculator">
                <h3>Custom Projection Calculator</h3>
                <p>Adjust parameters to see how different growth rates affect our breakeven timeline</p>
                
                <div class="calculator-form">
                    <div class="form-group">
                        <label for="userGrowth">New Users Per Month</label>
                        <input type="number" id="userGrowth" value="200" min="50" max="1000">
                    </div>
                    
                    <div class="form-group">
                        <label for="schoolGrowth">New Schools Per Quarter</label>
                        <input type="number" id="schoolGrowth" value="5" min="0" max="20">
                    </div>
                    
                    <div class="form-group">
                        <label for="churnRate">Monthly Churn Rate (%)</label>
                        <input type="number" id="churnRate" value="5" min="0" max="20">
                    </div>
                    
                    <div class="form-group">
                        <label for="premiumUpsell">Premium Upsell Rate (%)</label>
                        <input type="number" id="premiumUpsell" value="10" min="0" max="30">
                    </div>
                </div>
                
                <button class="btn" id="calculateBtn">Calculate Projections</button>
                
                <div class="calculator-results" id="calculatorResults" style="display: none;">
                    <h4>Projection Results</h4>
                    <div class="result-item">
                        <span class="label">Breakeven Month:</span>
                        <span class="value" id="breakevenMonth">Month 8</span>
                    </div>
                    <div class="result-item">
                        <span class="label">Year 1 Revenue:</span>
                        <span class="value" id="year1Revenue">R2,400,000</span>
                    </div>
                    <div class="result-item">
                        <span class="label">Year 2 Revenue:</span>
                        <span class="value" id="year2Revenue">R6,720,000</span>
                    </div>
                    <div class="result-item">
                        <span class="label">Active Users at Breakeven:</span>
                        <span class="value" id="breakevenUsers">1,600</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <footer id="contact">
        <div class="footer-content">
            <div class="footer-column">
                <h3>Solving Tomorrow</h3>
                <p>Empowering South African education through AI-driven learning solutions aligned with CAPS/IEB curricula.</p>
                <p>BEE Level 1 Contributor • POPIA Compliant</p>
            </div>
            
            <div class="footer-column">
                <h3>Contact Us</h3>
                <p><i class="fas fa-map-marker-alt"></i> 14 Education Ave, Sandton, Johannesburg</p>
                <p><i class="fas fa-phone"></i> +27 11 555 1234</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
            </div>
            
            <div class="footer-column">
                <h3>Quick Links</h3>
                <a href="#about">About Us</a>
                <a href="#team">Our Team</a>
                <a href="#revenue">Revenue Model</a>
                <a href="#">Privacy Policy</a>
            </div>
            
            <div class="footer-column">
                <h3>Investor Relations</h3>
                <a href="#">Business Plan</a>
                <a href="#">Financial Projections</a>
                <a href="#">Investment Opportunities</a>
                <a href="#">Schedule a Meeting</a>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2025 Solving Tomorrow (Pty) Ltd. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const navLinks = document.getElementById('navLinks');
        
        mobileMenuBtn.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });
        
        // Header Scroll Effect
        const header = document.getElementById('header');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });
        
        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                navLinks.classList.remove('active');
            });
        });
        
        // Revenue Chart
        const ctx = document.getElementById('revenueChart').getContext('2d');
        let revenueChart;
        
        // Chart Data
        const chartData = {
            optimistic: {
                labels: ['Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5', 'Month 6', 'Month 7', 'Month 8', 'Month 9', 'Month 10', 'Month 11', 'Month 12'],
                revenue: [100000, 200000, 300000, 400000, 500000, 600000, 700000, 800000, 900000, 1000000, 1100000, 1200000],
                breakeven: 3,
                users: [500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000]
            },
            realistic: {
                labels: ['Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5', 'Month 6', 'Month 7', 'Month 8', 'Month 9', 'Month 10', 'Month 11', 'Month 12'],
                revenue: [40000, 80000, 120000, 160000, 200000, 240000, 280000, 320000, 360000, 400000, 440000, 480000],
                breakeven: 8,
                users: [200, 400, 600, 800, 1000, 1200, 1400, 1600, 1800, 2000, 2200, 2400]
            },
            conservative: {
                labels: ['Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5', 'Month 6', 'Month 7', 'Month 8', 'Month 9', 'Month 10', 'Month 11', 'Month 12'],
                revenue: [20000, 40000, 60000, 80000, 100000, 120000, 140000, 160000, 180000, 200000, 220000, 240000],
                breakeven: 15,
                users: [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200]
            }
        };
        
        // Initialize Chart
        function initChart(scenario) {
            const data = chartData[scenario];
            
            if (revenueChart) {
                revenueChart.destroy();
            }
            
            revenueChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [
                        {
                            label: 'Monthly Revenue (R)',
                            data: data.revenue,
                            borderColor: '#FF6B35',
                            backgroundColor: 'rgba(255, 107, 53, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.3
                        },
                        {
                            label: 'Breakeven Point',
                            data: Array(data.labels.length).fill(300000),
                            borderColor: '#F7F7F7',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            fill: false,
                            pointRadius: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: `Revenue Projection: ${scenario.charAt(0).toUpperCase() + scenario.slice(1)} Scenario`,
                            color: '#F7F7F7',
                            font: {
                                size: 18
                            }
                        },
                        legend: {
                            labels: {
                                color: '#F7F7F7',
                                font: {
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR', maximumFractionDigits: 0 }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#F7F7F7'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#F7F7F7',
                                callback: function(value) {
                                    return new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR', maximumFractionDigits: 0 }).format(value);
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    },
                    annotation: {
                        annotations: {
                            line1: {
                                type: 'line',
                                yMin: 300000,
                                yMax: 300000,
                                borderColor: 'rgb(255, 99, 132)',
                                borderWidth: 2,
                                label: {
                                    content: 'Breakeven',
                                    enabled: true,
                                    position: 'left'
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // Scenario Buttons
        const scenarioButtons = document.querySelectorAll('.chart-controls button');
        
        scenarioButtons.forEach(button => {
            button.addEventListener('click', () => {
                scenarioButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                initChart(button.dataset.scenario);
            });
        });
        
        // Initialize with realistic scenario
        initChart('realistic');
        
        // Projection Calculator
        const calculateBtn = document.getElementById('calculateBtn');
        
        calculateBtn.addEventListener('click', () => {
            const userGrowth = parseInt(document.getElementById('userGrowth').value);
            const schoolGrowth = parseInt(document.getElementById('schoolGrowth').value);
            const churnRate = parseInt(document.getElementById('churnRate').value) / 100;
            const premiumUpsell = parseInt(document.getElementById('premiumUpsell').value) / 100;
            
            // Simplified calculation
            const monthlyUserRevenue = userGrowth * 200;
            const quarterlySchoolRevenue = schoolGrowth * 5000 * 3; // 3 months
            const monthlyPremiumRevenue = userGrowth * premiumUpsell * 50;
            
            const fixedCosts = 300000;
            const totalMonthlyRevenue = monthlyUserRevenue + (quarterlySchoolRevenue / 3) + monthlyPremiumRevenue;
            
            // Calculate breakeven
            let cumulativeRevenue = 0;
            let breakevenMonth = 0;
            let activeUsers = 0;
            
            for (let month = 1; month <= 24; month++) {
                activeUsers = userGrowth * month * (1 - churnRate);
                cumulativeRevenue = (activeUsers * 200) + (schoolGrowth * Math.floor(month/3) * 5000) + (activeUsers * premiumUpsell * 50);
                
                if (cumulativeRevenue >= fixedCosts * month) {
                    breakevenMonth = month;
                    break;
                }
            }
            
            // Update results
            document.getElementById('breakevenMonth').textContent = `Month ${breakevenMonth || '24+'}`;
            document.getElementById('year1Revenue').textContent = new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR', maximumFractionDigits: 0 }).format(
                (userGrowth * 12 * 200) + (schoolGrowth * 4 * 5000) + (userGrowth * 12 * premiumUpsell * 50)
            );
            document.getElementById('year2Revenue').textContent = new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR', maximumFractionDigits: 0 }).format(
                (userGrowth * 24 * 200) + (schoolGrowth * 8 * 5000) + (userGrowth * 24 * premiumUpsell * 50)
            );
            document.getElementById('breakevenUsers').textContent = Math.floor(userGrowth * breakevenMonth * (1 - churnRate)).toLocaleString();
            
            // Show results
            document.getElementById('calculatorResults').style.display = 'block';
        });
    </script>
</body>
</html>